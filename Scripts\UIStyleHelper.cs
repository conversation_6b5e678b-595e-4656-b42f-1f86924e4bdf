using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class UIStyleHelper : MonoBehaviour
{
    [Header("Card Settings")]
    public float cardWidth = 400f;
    public float cardHeight = 500f;
    
    [Header("Avatar Settings")]
    public float avatarSize = 80f;
    
    private void Start()
    {
        SetupLoginCardLayout();
    }
    
    private void SetupLoginCardLayout()
    {
        Transform loginCard = transform.Find("LoginCard");
        if (loginCard == null) return;
        
        RectTransform cardRect = loginCard.GetComponent<RectTransform>();
        if (cardRect != null)
        {
            cardRect.sizeDelta = new Vector2(cardWidth, cardHeight);
            cardRect.anchoredPosition = Vector2.zero;
            cardRect.anchorMin = new Vector2(0.5f, 0.5f);
            cardRect.anchorMax = new Vector2(0.5f, 0.5f);
            cardRect.pivot = new Vector2(0.5f, 0.5f);
        }
        
        SetupAvatar(loginCard);
        SetupWelcomeText(loginCard);
        SetupInputFields(loginCard);
        SetupButtons(loginCard);
    }
    
    private void SetupAvatar(Transform loginCard)
    {
        Transform avatar = loginCard.Find("AvatarImage");
        if (avatar == null) return;
        
        RectTransform avatarRect = avatar.GetComponent<RectTransform>();
        if (avatarRect != null)
        {
            avatarRect.sizeDelta = new Vector2(avatarSize, avatarSize);
            avatarRect.anchoredPosition = new Vector2(0, 180);
            avatarRect.anchorMin = new Vector2(0.5f, 0.5f);
            avatarRect.anchorMax = new Vector2(0.5f, 0.5f);
            avatarRect.pivot = new Vector2(0.5f, 0.5f);
        }
    }
    
    private void SetupWelcomeText(Transform loginCard)
    {
        Transform welcomeText = loginCard.Find("WelcomeText");
        if (welcomeText == null) return;
        
        RectTransform textRect = welcomeText.GetComponent<RectTransform>();
        if (textRect != null)
        {
            textRect.sizeDelta = new Vector2(cardWidth - 40, 40);
            textRect.anchoredPosition = new Vector2(0, 120);
            textRect.anchorMin = new Vector2(0.5f, 0.5f);
            textRect.anchorMax = new Vector2(0.5f, 0.5f);
            textRect.pivot = new Vector2(0.5f, 0.5f);
        }
    }
    
    private void SetupInputFields(Transform loginCard)
    {
        Transform usernameInput = loginCard.Find("UsernameInputField");
        if (usernameInput != null)
        {
            RectTransform inputRect = usernameInput.GetComponent<RectTransform>();
            if (inputRect != null)
            {
                inputRect.sizeDelta = new Vector2(cardWidth - 60, 50);
                inputRect.anchoredPosition = new Vector2(0, 50);
                inputRect.anchorMin = new Vector2(0.5f, 0.5f);
                inputRect.anchorMax = new Vector2(0.5f, 0.5f);
                inputRect.pivot = new Vector2(0.5f, 0.5f);
            }
        }
        
        Transform passwordInput = loginCard.Find("PasswordInputField");
        if (passwordInput != null)
        {
            RectTransform inputRect = passwordInput.GetComponent<RectTransform>();
            if (inputRect != null)
            {
                inputRect.sizeDelta = new Vector2(cardWidth - 60, 50);
                inputRect.anchoredPosition = new Vector2(0, -20);
                inputRect.anchorMin = new Vector2(0.5f, 0.5f);
                inputRect.anchorMax = new Vector2(0.5f, 0.5f);
                inputRect.pivot = new Vector2(0.5f, 0.5f);
            }
        }
    }
    
    private void SetupButtons(Transform loginCard)
    {
        Transform loginButton = loginCard.Find("LoginButton");
        if (loginButton != null)
        {
            RectTransform buttonRect = loginButton.GetComponent<RectTransform>();
            if (buttonRect != null)
            {
                buttonRect.sizeDelta = new Vector2(cardWidth - 60, 50);
                buttonRect.anchoredPosition = new Vector2(0, -100);
                buttonRect.anchorMin = new Vector2(0.5f, 0.5f);
                buttonRect.anchorMax = new Vector2(0.5f, 0.5f);
                buttonRect.pivot = new Vector2(0.5f, 0.5f);
            }
        }
        
        Transform signupButton = loginCard.Find("SignupButton");
        if (signupButton != null)
        {
            RectTransform buttonRect = signupButton.GetComponent<RectTransform>();
            if (buttonRect != null)
            {
                buttonRect.sizeDelta = new Vector2(cardWidth - 60, 30);
                buttonRect.anchoredPosition = new Vector2(0, -160);
                buttonRect.anchorMin = new Vector2(0.5f, 0.5f);
                buttonRect.anchorMax = new Vector2(0.5f, 0.5f);
                buttonRect.pivot = new Vector2(0.5f, 0.5f);
            }
        }
    }
}