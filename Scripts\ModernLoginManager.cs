using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

public class ModernLoginManager : MonoBehaviour
{
    [Header("UI References")]
    public TMP_InputField usernameInput;
    public TMP_InputField passwordInput;
    public Button loginButton;
    public Button signupButton;
    public TextMeshProUGUI welcomeText;
    public Image avatarImage;

    [Head<PERSON>("Visual Feedback")]
    public Color successColor = Color.green;
    public Color errorColor = Color.red;
    public Color normalColor = Color.white;

    [Header("Avatar Settings")]
    public Sprite[] avatarSprites;

    [Header("Demo Credentials")]
    [SerializeField] private string demoUsername = "admin";
    [SerializeField] private string demoPassword = "admin123";

    private void Start()
    {
        InitializeUI();
        SetupEventListeners();
        SetRandomAvatar();
    }

    private void InitializeUI()
    {
        if (welcomeText != null)
            welcomeText.text = "Welcome back";

        if (usernameInput != null && usernameInput.placeholder != null)
            usernameInput.placeholder.GetComponent<TextMeshProUGUI>().text = "username";

        if (passwordInput != null && passwordInput.placeholder != null)
        {
            passwordInput.placeholder.GetComponent<TextMeshProUGUI>().text = "password";
            passwordInput.contentType = TMP_InputField.ContentType.Password;
        }

        ValidateInputs();
    }

    private void SetupEventListeners()
    {
        if (loginButton != null)
            loginButton.onClick.AddListener(OnLoginButtonClicked);

        if (signupButton != null)
            signupButton.onClick.AddListener(OnSignupButtonClicked);

        if (usernameInput != null)
            usernameInput.onValueChanged.AddListener(OnInputChanged);

        if (passwordInput != null)
            passwordInput.onValueChanged.AddListener(OnInputChanged);
    }

    private void SetRandomAvatar()
    {
        if (avatarImage != null && avatarSprites != null && avatarSprites.Length > 0)
        {
            int randomIndex = Random.Range(0, avatarSprites.Length);
            avatarImage.sprite = avatarSprites[randomIndex];
        }
    }

    private void OnInputChanged(string value)
    {
        ValidateInputs();
    }

    private void ValidateInputs()
    {
        bool isValid = !string.IsNullOrEmpty(usernameInput?.text) &&
                      !string.IsNullOrEmpty(passwordInput?.text) &&
                      passwordInput.text.Length >= 3;

        if (loginButton != null)
        {
            loginButton.interactable = isValid;
            ColorBlock colors = loginButton.colors;
            colors.normalColor = isValid ? new Color(0.2f, 0.6f, 1f, 1f) : new Color(0.7f, 0.7f, 0.7f, 1f);
            loginButton.colors = colors;
        }
    }

    public void OnLoginButtonClicked()
    {
        if (ValidateLogin())
        {
            StartCoroutine(PerformLogin());
        }
    }

    private bool ValidateLogin()
    {
        string username = usernameInput.text.Trim();
        string password = passwordInput.text;

        if (string.IsNullOrEmpty(username))
        {
            ShowFeedback("Please enter a username", errorColor);
            return false;
        }

        if (string.IsNullOrEmpty(password))
        {
            ShowFeedback("Please enter a password", errorColor);
            return false;
        }

        if (password.Length < 3)
        {
            ShowFeedback("Password must be at least 3 characters", errorColor);
            return false;
        }

        return true;
    }

    private IEnumerator PerformLogin()
    {
        loginButton.interactable = false;

        TextMeshProUGUI buttonText = loginButton.GetComponentInChildren<TextMeshProUGUI>();
        string originalText = buttonText.text;
        buttonText.text = "LOGGING IN...";

        yield return new WaitForSeconds(1.5f);

        string username = usernameInput.text.Trim();
        string password = passwordInput.text;

        if (SimulateLoginValidation(username, password))
        {
            ShowFeedback("Login successful!", successColor);
            yield return new WaitForSeconds(1f);
            Debug.Log($"User {username} logged in successfully!");
            ResetForm();
        }
        else
        {
            ShowFeedback("Invalid username or password", errorColor);
        }

        buttonText.text = originalText;
        loginButton.interactable = true;
    }

    private bool SimulateLoginValidation(string username, string password)
    {
        return (username.ToLower() == demoUsername.ToLower() && password == demoPassword) ||
               (username.Length > 0 && password.Length >= 3);
    }

    public void OnSignupButtonClicked()
    {
        Debug.Log("Signup button clicked - would navigate to signup screen");
        ShowFeedback("Signup feature coming soon!", new Color(0.2f, 0.6f, 1f, 1f));
    }

    private void ShowFeedback(string message, Color color)
    {
        Debug.Log($"Feedback: {message}");

        if (welcomeText != null)
        {
            StartCoroutine(ShowTemporaryMessage(message, color));
        }
    }

    private IEnumerator ShowTemporaryMessage(string message, Color color)
    {
        string originalText = welcomeText.text;
        Color originalColor = welcomeText.color;

        welcomeText.text = message;
        welcomeText.color = color;

        yield return new WaitForSeconds(3f);

        welcomeText.text = originalText;
        welcomeText.color = originalColor;
    }

    private void ResetForm()
    {
        if (usernameInput != null)
            usernameInput.text = "";

        if (passwordInput != null)
            passwordInput.text = "";

        ValidateInputs();
    }

    public void SetAvatar(Sprite newAvatar)
    {
        if (avatarImage != null)
            avatarImage.sprite = newAvatar;
    }

    public void LoginWithCredentials(string username, string password)
    {
        if (usernameInput != null)
            usernameInput.text = username;

        if (passwordInput != null)
            passwordInput.text = password;

        OnLoginButtonClicked();
    }

    [ContextMenu("Test Login")]
    public void TestLogin()
    {
        LoginWithCredentials(demoUsername, demoPassword);
    }
}